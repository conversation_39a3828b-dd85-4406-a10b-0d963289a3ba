const React = require('react')
const db = require('../../db')
const config = require('../../../config')
const s3 = require('../../util/S3')
const GrafanaEmbed = require('./Components/GrafanaEmbed')
const grafanaToken = config.grafanaServer.token

const grafanaDashboard = async(module, report) => {
  const grafanaDashboard = await db.grafanaDashboard.findOne({ module, report })

  if (!grafanaDashboard) return null
  
  return `${grafanaDashboard.url}&type=${grafanaDashboard.module}${grafanaDashboard.report}&from=${grafanaToken}`
}
  
const grafanaCache = async({ uniqueReportId, results, owner, type, skip = 0, limit, filters, state, module, additional, processDataForGrafana }) => {
  // save cache data to db
  const existGrafanaReport = await db.grafanaReport.findOne({ owner: owner, type: type, progressIndicators: JSON.stringify({ skip, limit }), params: stringfyFilter(filters), module })

  if (existGrafanaReport) {
    uniqueReportId = existGrafanaReport.uniqueReportId
    existGrafanaReport.additional = additional

    await existGrafanaReport.save()
  } else {
    const newGrafanaReportObj = { 
      uniqueReportId,
      owner: owner,
      type: type,
      params: stringfyFilter(filters),
      expireAt: new Date(),
      module,
      progressIndicators: JSON.stringify({ skip, limit }),
      additional,
    }
    const newGrafanaReport = new db.grafanaReport(newGrafanaReportObj)

    await newGrafanaReport.save()
  }

  const cachedResult = processDataForGrafana(results, state, type)

  s3.upload({
    key: `grafana-reports/${uniqueReportId}_${skip}_${limit}.json`,
    body: Buffer.from(cachedResult)
  })

  return uniqueReportId
}

const grafanaCacheChunk = async({ owner, type, skip, limit, filters, module }) => {
  const existGrafanaReport = await db.grafanaReport.findOne({ owner: owner, type, progressIndicators: JSON.stringify({ skip: skip - limit, limit }), params: stringfyFilter(filters), module })
  
  if (existGrafanaReport) return existGrafanaReport
  
  return null
}

const stringfyFilter = (filter) => (
  JSON.stringify(filter, function(key, value) {
    if (value === undefined) return '__undefined__'

    if (typeof value === 'object' && value !== null) {

      return Object.entries(value).reduce((acc, [k, v]) => {
        if (k.startsWith('$')) {
          acc[`_dollar_${k.slice(1)}`] = v
        } else {
          acc[k] = v
        }

        return acc
      }, Array.isArray(value) ? [] : {})
    }

    return value
  })
)

const parseFilter = (filterString) => {

  return JSON.parse(filterString, function(key, value) {
    if (value === '__undefined__') return value

    if (typeof value === 'object' && value !== null) {

      return Object.entries(value).reduce((acc, [k, v]) => {
        if (k.startsWith('_dollar_')) {
          acc[`$${k.slice(8)}`] = v === '__undefined__' ? undefined : v
        } else {
          acc[k] = v === '__undefined__' ? undefined : v
        }

        return acc
      }, Array.isArray(value) ? [] : {})
    }

    return value
  })
}

const grafanaDashboardEmbed = async({ module, type, grafanaReport, fileName }) => {
  const iframeSrc = await grafanaDashboard(module, type)

  return React.createElement('div', { style: { display: 'flex', flexDirection: 'column', margin: '0 10px' } },
    React.createElement(GrafanaEmbed, { iframeSrc: `${iframeSrc}&to=${grafanaReport}&filename=${fileName}` })
  )
}

module.exports = {
  grafanaDashboard,
  grafanaCache,
  grafanaCacheChunk,
  stringfyFilter,
  parseFilter,
  grafanaDashboardEmbed,
}