const db = require('../../db')
const stringify = require('csv-stringify')
const Functions = require('../../util/Functions')
const StandardGrafana = require('../Standard/Reports/Grafana')
const LegislationsGrafana = require('../Standard/Legislation/Reports/Grafana')
const TrackingGrafana = require('../Tracking/Reports/Grafana')
const ChecklistGrafana = require('../Checklist/Reports/Grafana')
const ProductGrafana = require('../Product/Reports/Grafana')

const PAGE_SIZE = 100

const processReportBatch = async(to, page, csvStream, module) => {
  const skip = (page - 1) * PAGE_SIZE
  const isFirstPage = page === 1
  const startTime = Date.now()
  
  try {
    const reports = await db.grafanaReport
      .find({ uniqueReportId: to })
      .skip(skip)
      .limit(PAGE_SIZE)
      .maxTimeMS(30000) // 30 second query timeout
      .hint({ uniqueReportId: 1 }) // Ensure index usage
      .lean()

    const queryTime = Date.now() - startTime

    console.log(`Database query for page ${page} took ${queryTime}ms`)

    if (reports.length === 0) {
      return false
    }
    
    const processStartTime = Date.now()
    
    switch (module) {
      case 'tracking':
        await TrackingGrafana.getGrafanaReports(reports, csvStream, isFirstPage)
        break
      case 'standard':
        await StandardGrafana.getGrafanaReports(reports, csvStream, isFirstPage)
        break
      case 'checklist':
        await ChecklistGrafana.getGrafanaReports(reports, csvStream, isFirstPage)
        break
      case 'product':
        await ProductGrafana.getGrafanaReports(reports, csvStream, isFirstPage)
        break
      case 'legislations':
        await LegislationsGrafana.getGrafanaReports(reports, csvStream, isFirstPage)
        break
      default:
        break
    }

    const processTime = Date.now() - processStartTime

    console.log(`Processing ${reports.length} reports for page ${page} took ${processTime}ms`)

    return reports.length === PAGE_SIZE
  } catch (error) {
    console.error(`Error in processReportBatch for page ${page}:`, error)
    throw error
  }
}

// Cleanup function for properly closing all streams
function cleanup(res, stringifier, fileStream, heartbeatInterval = null, isClientClosed = false, isCleaningUp = false) {
  if (isCleaningUp) return
  console.log('Cleaning up streams...')

  try {
    // Clear heartbeat interval first
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
      console.log('Heartbeat interval cleared')
    }

    if (stringifier && !stringifier.destroyed) {
      try {
        if (stringifier.writable) {
          stringifier.end()
        }
        stringifier.destroy()
      } catch (err) {
        console.error('Error closing stringifier:', err.message)
      }
    }

    if (fileStream && !fileStream.destroyed) {
      try {
        if (fileStream.writable) {
          fileStream.end()
        }
        fileStream.destroy()
      } catch (err) {
        console.error('Error closing fileStream:', err.message)
      }
    }

    if (!res.finished && !isClientClosed && !res.headersSent) {
      try {
        res.end()
      } catch (err) {
        console.error('Error closing response:', err.message)
      }
    }
  } catch (err) {
    console.error('Error during cleanup:', err)
  }
}

/**
 * Get Grafana Report Data as CSV
 * @param req
 * @param res
 */
module.exports = async(req, res) => {
  console.log('Grafana report request received')

  const { to } = req.query
  let stringifier = null
  let isCleaningUp = false
  let heartbeatInterval = null
  
  if (Functions.isNull(to)) return res.sendError('009')
  
  try {
    // Immediately write an initial response to prevent curl waiting
    res.writeHead(200, {
      'X-Accel-Buffering': 'no',
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="export-${to}.csv"`,
      'Transfer-Encoding': 'chunked',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Connection': 'keep-alive',
      'Keep-Alive': 'timeout=0'
    })
    
    // Send initial empty line to force headers to be sent
    res.write('\n')
    
    // Configure socket with enhanced timeout settings
    if (res.socket) {
      res.socket.setTimeout(0) // Disable socket timeout
      res.socket.setNoDelay(true)
      res.socket.setKeepAlive(true, 10000) // 30 second keep-alive
    }

    // Set up heartbeat mechanism - send a comment line every 10 seconds to keep connection alive
    heartbeatInterval = setInterval(() => {
      if (!res.finished && res.writable && !isCleaningUp) {
        console.info('heartbeatInterval:', !res.finished, res.writable, isCleaningUp)

        try {
          res.write('')
        } catch (error) {
          console.error('Error sending heartbeat:', error)
          clearInterval(heartbeatInterval)
        }
      } else {
        clearInterval(heartbeatInterval)
      }
    }, 10000)
    
    // Fetch report data after headers are sent
    const firstReport = await db.grafanaReport.findOne({ uniqueReportId: to })

    if (!firstReport) {
      res.end()

      return
    }
    
    let currentPage = 1
    let hasMore = true
    
    // Create stringifier once
    stringifier = stringify({
      highWaterMark: 16 * 1024
    })
    
    // Direct pipe to response
    stringifier.pipe(res)

    // Register cleanup handler for client disconnect
    req.on('close', () => {

      if (!isCleaningUp) {
        isCleaningUp = true
        cleanup(res, stringifier, null, heartbeatInterval, true, isCleaningUp)
      }
    })
    
    // Process each batch
    while (hasMore) {

      try {
        hasMore = await processReportBatch(to, currentPage, stringifier, firstReport.module)
      } catch (error) {
        console.error(`Error processing page ${currentPage}:`, error)
        throw error
      }

      currentPage++

      if (currentPage % 5 === 0) {
        await new Promise(resolve => setImmediate(resolve))
      }
    }

    // Finish stringifier to end streams properly
    if (stringifier && stringifier.writable) {
      stringifier.end()
    }
    
    // Clear heartbeat interval on successful completion
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
    
    // Wait for file to finish writing
    await new Promise(resolve => {
      resolve()
    })
    
  } catch (error) {
    
    // Clear heartbeat interval on error
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
    
    if (!res.headersSent) {
      res.writeHead(500)
    }
    
    if (!res.finished) {
      res.end()
    }
  } finally {
    // Final cleanup to ensure heartbeat is always cleared
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
  }
}
