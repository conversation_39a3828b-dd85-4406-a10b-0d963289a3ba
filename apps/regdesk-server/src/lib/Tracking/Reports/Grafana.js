const { getUTC } = require('../../../util/Date')
const { accumulateSku, filterFamilyProducts, getPartNumbers, getSubmission } = require('./utils/helpers')
const { TRACKING_STATUS_CODES: { APPROVED, REQUESTED, PREPARING, GOVERNMENT, REJECTED, INTERNAL, SUBMITTED, INFORMATION, RENEWAL } } = require('../utils')
const { getDiscontinueWithDateFormat } = require('./NumberOfAdditionalInformationRequests/utils')
const s3 = require('../../../util/S3')
const { Transform } = require('stream')
const timeFormat = 'YYYY-MM-DD hh:mm:ss'
const BATCH_SIZE = 200
const baseColumns = { 
  status: 'status',
  id: 'trackingId', 
  name: 'trackingName', 
  countryId: 'country', 
  product: 'product',
  classification: 'classification',
  productFamily: 'productFamily',
  licenseType: 'licenseType',
  typeSubmission: 'typeSubmission',
  partNumber: 'partNumber',
  createdAt: 'createdAt',
  owner: 'owner',
  licenseNumber: 'licenseNumber',
  approvalDate: 'approvalDate',
  dateExpiry: 'expiryDate',
  expectedApprovalDate: 'expectedApprovalDate',
  rejectedDate: 'rejectedDate',
  governmentDate: 'governmentDate',
  preparedDate: 'preparedDate',
  requestedDate: 'requestedDate',
  registerNumber: 'registerNumber',
  sku: 'sku',
  skuDescription: 'skuDescription',
  expectedSubmissionDate: 'expectedSubmissionDate',
  actualSubmissionDate: 'actualSubmissionDate',
  comment: 'comment',
  topCountries: 'topCountries',
  topProduct: 'topProduct',
  actualApprovalDate: 'actualApprovalDate',
  min: 'min',
  max: 'max',
  avg: 'avg',
}
const baseDefault = {
  id: '',
  name: '',
  countryId: '',
  product: '',
  sku: '',
  skuDescription: '',
  productFamily: '',
  licenseNumber: '',
  licenseType: '',
  typeSubmission: '',
  partNumber: '',
  approvalDate: '',
  dateExpiry: '',
  expectedApprovalDate: '',
  rejectedDate: '',
  governmentDate: '',
  preparedDate: '',
  requestedDate: '',
  expectedSubmissionDate: '',
  actualSubmissionDate: '',
  registerNumber: '',
  classification: '',
  createdAt: '',
  owner: '',
  status: '',
  comment: '',
  topCountries: '',
  topProduct: '',
  actualApprovalDate: '',
  min: '',
  max: '',
  avg: '',
}
const getCsvConfig = (reportType) => {
  const columns = { ...baseColumns }
  const defaultData = { ...baseDefault }

  if (reportType.includes('18')) {
    columns.startDate = 'startDate'
    columns.reason = 'reason'
    defaultData.startDate = ''
    defaultData.reason = ''
  }

  if (reportType.includes('19')) {
    columns.assignee = 'assignee'
    columns.checklistAssociated = 'checklistAssociated'
    columns.preparedExpectedSubmissionDate = 'preparedExpectedSubmissionDate'
    columns.governmentExpectedSubmissionDate = 'governmentExpectedSubmissionDate'
    columns.governmentActualSubmissionDate = 'governmentActualSubmissionDate'
    columns.preparedExpectedApprovalDate = 'preparedExpectedApprovalDate'
    defaultData.assignee = ''
    defaultData.preparedExpectedSubmissionDate = ''
    defaultData.governmentExpectedSubmissionDate = ''
    defaultData.governmentActualSubmissionDate = ''
    defaultData.preparedExpectedApprovalDate = ''
    defaultData.checklistAssociated = ''
  }

  return { columns, defaultData }
}
const simplifyLogs = (logs) => {
  if (!logs) return []

  let sortedLogs = [...logs].sort((a, b) => b.timestamps - a.timestamps)
  const requestedLog = sortedLogs.find(log => log.event === 'requestAgain')

  if (requestedLog) {
    sortedLogs = sortedLogs.filter(log => log.timestamps >= requestedLog.timestamps)
  }
  
  return sortedLogs
}
const getFormattedDate = (log, field) => log && log[field] ? getUTC(log[field], timeFormat) : null
const getFirstAvailableUTC = (log, fields) => {
  if (!log) return null

  for (const field of fields) {
    if (log[field]) return getUTC(log[field], timeFormat)
  }

  return null
}
const getStatusDate = (statuses, statusKey, field) =>
  statuses && statuses[statusKey] && statuses[statusKey][field]
    ? getUTC(statuses[statusKey][field], timeFormat)
    : null
const getLogByStatus = (logs, status) => logs.find(log => log.status === status)

const getGrafanaReports = async(existGrafanaReports, csvStream, isFirstPage) => {
  const firstType = existGrafanaReports && existGrafanaReports[0]?.type || ''
  const { columns, defaultData } = getCsvConfig(firstType)

  isFirstPage && csvStream.write(columns)

  if (!existGrafanaReports || existGrafanaReports.length === 0) {
    csvStream.write(defaultData)

    return
  }

  return new Promise((resolve) => {
    let reportIndex = 0
    
    const processBatch = async() => {
      if (reportIndex >= existGrafanaReports.length) {
        resolve()

        return
      }

      const currentBatch = existGrafanaReports.slice(reportIndex, reportIndex + BATCH_SIZE)
      const batchPromises = currentBatch.map(async(report) => {
        const { skip, limit } = JSON.parse(report.progressIndicators)
        const s3Key = `grafana-reports/${report.uniqueReportId}_${skip}_${limit}.json`

        try {
          const exists = await s3.existObj(s3Key)

          if (!exists) {
            console.warn(`S3 file not found: ${s3Key}`)

            return
          }

          const processDataStream = new TrackingDataTransform()
          const s3Stream = s3.get(s3Key).createReadStream()

          processDataStream.report = report

          await new Promise((streamResolve, streamReject) => {
            // Track written items
            let itemsWritten = 0
            
            // Handle progress events from the transform stream
            processDataStream.on('progress', (progress) => {
              console.log(`Progress for report ${report.uniqueReportId}: ${progress.processed} items processed`)
              
              // Force flush data regularly
              if (csvStream.flush && typeof csvStream.flush === 'function') {
                csvStream.flush()
              }
            })
            
            s3Stream
              .pipe(processDataStream)
              .on('data', (data) => {
                // Handle backpressure by checking write result
                const canContinue = csvStream.write(data)

                itemsWritten++
                
                // If the stream buffer is full, wait for drain event
                if (!canContinue && csvStream.once) {
                  processDataStream.pause()

                  csvStream.once('drain', () => {
                    processDataStream.resume()
                  })
                }
                
                // Log progress periodically
                if (itemsWritten % 100 === 0) {
                  console.log(`Written ${itemsWritten} items for report ${report.uniqueReportId}`)
                }
              })
              .on('error', (error) => {
                console.error(`Stream error for report ${report.uniqueReportId}:`, error)
                streamReject(error)
              })
              .on('end', () => {
                console.log(`Finished processing report ${report.uniqueReportId}: ${itemsWritten} items written`)
                
                // Final flush
                if (csvStream.flush && typeof csvStream.flush === 'function') {
                  csvStream.flush()
                }
                
                streamResolve()
              })
          })
        } catch (error) {
          console.error(`Error processing report ${report.uniqueReportId}:`, error)
        }
      })

      try {
        await Promise.all(batchPromises)
      } catch (error) {
        console.error('Batch processing error:', error)
      }

      reportIndex += BATCH_SIZE
      setImmediate(processBatch)
    }

    processBatch()
  })
}
const getLatestDate = (...dates) => {
  return dates.reduce((latestDate, currentDate) => {
    if (!currentDate) return latestDate
    if (!latestDate) return currentDate

    return new Date(currentDate) > new Date(latestDate) ? currentDate : latestDate
  }, null)
}

class TrackingDataTransform extends Transform {
  constructor(options = {}) {
    super({ 
      ...options, 
      objectMode: true,
      highWaterMark: 16 * 1024 // Reduced to 16KB for more frequent data streaming
    })
    this.buffers = [] // Store chunks as Buffer objects
    this.bufferSize = 0
    this.pushCount = 0
    this.emitCount = 0
    
    // Performance monitoring
    this.startTime = Date.now()
    this.bytesProcessed = 0
    this.itemsProcessed = 0
    
    // Log performance every 5 seconds
    this.performanceInterval = setInterval(() => {
      this.logPerformance()
    }, 5000)
    
    // Emit progress event more frequently
    this.progressInterval = setInterval(() => {
      this.emitProgress()
    }, 500) // Every 500ms
  }
  
  emitProgress() {
    if (this.itemsProcessed > 0 && this.emitCount !== this.itemsProcessed) {
      this.emitCount = this.itemsProcessed

      this.emit('progress', {
        processed: this.itemsProcessed,
        bytesProcessed: this.bytesProcessed,
        timestamp: new Date().toISOString()
      })
    }
  }

  _transform(chunk, encoding, callback) {
    try {
      // Track bytes processed for performance monitoring
      this.bytesProcessed += chunk.length
      
      // Store chunk as Buffer for better memory efficiency
      this.buffers.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk))
      this.bufferSize += chunk.length
      
      // Try to parse complete JSON objects
      try {
        // Convert buffers to string only when needed
        const data = Buffer.concat(this.buffers).toString()
        
        // Parse the JSON data
        const outputResults = JSON.parse(data)
        
        // Clear buffer after successful parse
        this.buffers = []
        this.bufferSize = 0
        
        // Optimize batch processing with larger batch size
        const BATCH_SIZE = 200 // Increased batch size for better throughput
        
        // Process all items in a more efficient way
        if (outputResults && Array.isArray(outputResults)) {
          this.itemsProcessed += outputResults.length
          
          // Fast path for small arrays - avoid batch processing overhead
          if (outputResults.length <= BATCH_SIZE) {
            for (const item of outputResults) {
              // Optimize data before pushing
              const optimizedItem = this.optimizeDataItem(item)

              this.pushCount++
              this.push(optimizedItem)
            }

            return callback()
          }
          
          // For larger arrays, use optimized batch processing
          let i = 0
          const processNextBatch = () => {
            let batchCount = 0
            let shouldContinue = true
            
            // Process a batch of items
            while (i < outputResults.length && batchCount < BATCH_SIZE && shouldContinue) {
              // Optimize data before pushing
              const optimizedItem = this.optimizeDataItem(outputResults[i++])

              this.pushCount++
              shouldContinue = this.push(optimizedItem)
              batchCount++
            }
            
            // If we've processed all items, we're done
            if (i >= outputResults.length) {
              return callback()
            }
            
            // If we hit backpressure, wait for drain before continuing
            if (!shouldContinue) {
              this.once('drain', () => setImmediate(processNextBatch))
            } else {
              // Continue processing in next tick to avoid stack overflow
              setImmediate(processNextBatch)
            }
          }
          
          // Start batch processing
          processNextBatch()
        } else {
          // Handle non-array data
          callback()
        }
      } catch (parseError) {
        if (parseError instanceof SyntaxError) {
          // Incomplete JSON, wait for more data
          callback()
        } else {
          callback(parseError)
        }
      }
    } catch (error) {
      console.error('Error in transform:', error)
      callback(error)
    }
  }

  // Optimize data item by removing null/undefined values and unnecessary fields
  optimizeDataItem(item) {
    if (!item || typeof item !== 'object') return item
    
    const optimized = { ...item }
    
    // Remove null or undefined values to reduce data size
    Object.keys(optimized).forEach(key => {
      if (optimized[key] === null || optimized[key] === undefined) {
        delete optimized[key]
      }
    })
    
    return optimized
  }

  _flush(callback) {
    try {
      if (this.bufferSize > 0) {
        const data = Buffer.concat(this.buffers).toString()

        try {
          const outputResults = JSON.parse(data)
          
          // Process any remaining data
          if (Array.isArray(outputResults)) {
            for (const row of outputResults) {
              const optimizedItem = this.optimizeDataItem(row)

              this.push(optimizedItem)
            }
          }
        } catch (error) {
          console.warn('Ignored incomplete data in flush:', error)
        }
      }
      
      // Log final performance stats
      this.logPerformance(true)
      
      // Clear performance interval
      clearInterval(this.performanceInterval)
      
      callback()
    } catch (error) {
      console.warn('Error in flush:', error)
      callback()
    }
  }
  
  // Log performance metrics
  logPerformance(isFinal = false) {
    const elapsedMs = Date.now() - this.startTime

    if (elapsedMs === 0) return
    
    const mbProcessed = this.bytesProcessed / (1024 * 1024)
    const itemsPerSecond = (this.itemsProcessed * 1000) / elapsedMs
    const mbPerSecond = (mbProcessed * 1000) / elapsedMs
    
    const logPrefix = isFinal ? 'Final stats' : 'Performance'

    console.log(`${logPrefix}: Processed ${mbProcessed.toFixed(2)}MB (${this.itemsProcessed} items) in ${(elapsedMs / 1000).toFixed(2)}s - ${mbPerSecond.toFixed(2)}MB/s, ${itemsPerSecond.toFixed(2)} items/s`)
  }
}

const processForGrafana = {
  '1': {
    process: (trackings, state) => {
      // Time for Preparation - returns trackings with min, max, avg values
      return trackings.map(result => {
        const processedResult = processTrackingData(result, state, '1')

        return {
          ...processedResult,
          min: result.min,
          max: result.max,
          avg: result.avg
        }
      })
    }
  },
  '2': {
    process: (trackings, state) => {
      // Countries with Market Approval - filters for approved status
      return trackings
        .filter(result => result.status === APPROVED)
        .map(result => processTrackingData(result, state, '2'))
    }
  },
  '3': {
    process: (trackings, state) => {
      // Status of License Type Report - filters for products with SKUs
      return trackings
        .filter(result => {
          const { products = [], sku } = result

          if (Array.isArray(products) && products.length) {
            const product = products[0]
            const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

            return productSku.length > 0
          }

          return true
        })
        .map(result => processTrackingData(result, state, '3'))
    }
  },
  '4': {
    process: (trackings) => {
      // Time to Prepare Application in a Country - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '5': {
    process: (trackings) => {
      // Total Government Review Time
      return trackings
    }
  },
  '6': {
    process: (trackings) => {
      // Status of a submission type globally - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '7': {
    process: (trackings) => {
      // Status of Product in a Country
      return trackings
    }
  },
  '8': {
    process: (trackings) => {
      // Products ready for Sale - filters for approved status and products with SKUs
      return trackings.filter(result => {
        if (result.status !== APPROVED) return false

        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '9': {
    process: (trackings) => {
      // Countries where Product can be sold - filters for approved status and products with SKUs
      return trackings.filter(result => {
        if (result.status !== APPROVED) return false

        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '10': {
    process: (trackings) => {
      // Status of Product in MDSAP Countries - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '11': {
    process: (trackings) => {
      // Registration Status - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '12': {
    process: (trackings) => {
      // Registration Status for Change Assessment - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '13': {
    process: (trackings) => {
      // Total Approval time in Country - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '14': {
    process: (trackings) => {
      // License expiration by a time period - filters for products with SKUs
      return trackings.filter(result => {
        const { products = [], sku } = result

        if (Array.isArray(products) && products.length) {
          const product = products[0]
          const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())

          return productSku.length > 0
        }

        return true
      })
    }
  },
  '15': {
    process: (trackings) => {
      // KPIs of Status Progress - adds topCountries and topProduct flags
      return trackings.map(result => {
        const { products = [] } = result
        const topCountries = (products[0] && products[0].topCountries) ? products[0].topCountries.join(', ') : null
        const topProduct = (products[0] && products[0].topProduct) ? 'yes' : null

        return {
          ...result,
          topCountries,
          topProduct
        }
      })
    }
  },
  '16': {
    process: (trackings) => {
      // Regulatory Status of Product - no product filtering for this report
      return trackings
    }
  },
  '17': {
    process: (trackings) => {
      // Registrations in Process
      return trackings
    }
  },
  '18': {
    process: (trackings) => {
      // Number of Additional Information Requests - special processing for date/reason
      return trackings.map(result => {
        const dateStartReasonList = getDiscontinueWithDateFormat(result.logs)
        const { productFamilyProducts, products = [] } = result
        const productNames = (productFamilyProducts?.length > 0 ? productFamilyProducts : products).map(({ name }) => name).join(', ')
        
        return {
          ...result,
          dateStartReasonList,
          productNames
        }
      })
    }
  },
  '19': {
    process: (trackings) => {
      // Monthly On Time Delivery - adds assignee and additional date fields
      return trackings.map(result => {
        const { statuses, status } = result
        const assignee = (status !== RENEWAL) ? (statuses && statuses[status]?.assignee?.name || 'N/A')
          : (statuses && statuses[APPROVED]?.assignee?.name || 'N/A')
        
        const preparedExpectedSubmissionDate = getStatusDate(statuses, PREPARING, 'expectedSubmissionDate')
        const governmentExpectedSubmissionDate = getStatusDate(statuses, GOVERNMENT, 'expectedSubmissionDate')
        const governmentActualSubmissionDate = statuses && statuses[GOVERNMENT] && statuses[GOVERNMENT].actualSubmissionDate ? getUTC(statuses[GOVERNMENT].actualSubmissionDate, timeFormat) : ''
        const preparedExpectedApprovalDate = getStatusDate(statuses, PREPARING, 'expectedApprovalDate')
        
        return {
          ...result,
          assignee,
          preparedExpectedSubmissionDate,
          governmentExpectedSubmissionDate,
          governmentActualSubmissionDate,
          preparedExpectedApprovalDate
        }
      })
    }
  },
}

/**
 * Process tracking data to convert object fields to strings
 * @param {Object} result - The tracking result object
 * @param {Object} state - The state object
 * @param {String} type - The report type
 * @returns {Object} - The processed tracking data
 */
const processTrackingData = (result, state, type) => {
  const { statuses, productFamily, products = [], productFamilyProducts, sku, countryId } = result
  const logs = simplifyLogs(result.logs, result.status)
  const currentLog = getLogByStatus(logs, result.status)
  const requestedLog = getLogByStatus(logs, REQUESTED)
  const approveLog = getLogByStatus(logs, APPROVED)
  const rejectedLog = getLogByStatus(logs, REJECTED)
  const preparedLog = getLogByStatus(logs, PREPARING)
  const governmentLog = getLogByStatus(logs, GOVERNMENT)
  const licenseNumber = currentLog ? currentLog.license : null
  const licenseType = currentLog ? (currentLog.certificate && currentLog.certificate.name) : null
  const approvalDate = getFormattedDate(approveLog, 'dateDiscontinue')
  const dateExpiry = getFormattedDate(approveLog, 'dateExpiry')
  const rejectedDate = getFormattedDate(rejectedLog, 'dateDiscontinue')
  const governmentDate = getFirstAvailableUTC(governmentLog, ['dateStart', 'timestamps'])
  const preparedDate = getFirstAvailableUTC(preparedLog, ['dateStart', 'timestamps'])
  const requestedDate = getFirstAvailableUTC(requestedLog, ['dateSubmitted', 'timestamps'])
  const preparedExpectedSubmissionDate = getStatusDate(statuses, PREPARING, 'expectedSubmissionDate')
  const internalExpectedSubmissionDate = getStatusDate(statuses, INTERNAL, 'expectedSubmissionDate')
  const submittedExpectedSubmissionDate = getStatusDate(statuses, SUBMITTED, 'expectedSubmissionDate')
  const governmentExpectedSubmissionDate = getStatusDate(statuses, GOVERNMENT, 'expectedSubmissionDate')
  const expectedSubmissionDate = getLatestDate(preparedExpectedSubmissionDate, internalExpectedSubmissionDate, submittedExpectedSubmissionDate)
  const governmentActualSubmissionDate = statuses && statuses[GOVERNMENT] && statuses[GOVERNMENT].actualSubmissionDate ? getUTC(statuses[GOVERNMENT].actualSubmissionDate, timeFormat) : ''
  const informationActualSubmissionDate = statuses && statuses[INFORMATION] && statuses[INFORMATION].actualSubmissionDate ? getUTC(statuses[INFORMATION].actualSubmissionDate, timeFormat) : ''
  const actualSubmissionDate = (governmentActualSubmissionDate && informationActualSubmissionDate)
    ? `${governmentActualSubmissionDate}, ${informationActualSubmissionDate}`
    : (governmentActualSubmissionDate || informationActualSubmissionDate)
  const actualApprovalDate = getStatusDate(statuses, APPROVED, 'dateDiscontinue')
  const preparedExpectedApprovalDate = getStatusDate(statuses, PREPARING, 'expectedApprovalDate')
  const internalExpectedApprovalDate = getStatusDate(statuses, INTERNAL, 'expectedApprovalDate')
  const submittedExpectedApprovalDate = getStatusDate(statuses, SUBMITTED, 'expectedApprovalDate')
  const governmentExpectedApprovalDate = getStatusDate(statuses, GOVERNMENT, 'expectedApprovalDate')
  const expectedApprovalDate = getLatestDate(preparedExpectedApprovalDate, internalExpectedApprovalDate, submittedExpectedApprovalDate, governmentExpectedApprovalDate)
  const registerNumber = preparedLog ? preparedLog.registerNumber : null
  const classification = result.classification?.length > 0 ? result.classification.find(c => c.id === result.country)?.name : null
  const topCountries = (type === '15' && products[0] && products[0].topCountries) ? products[0].topCountries.join(', ') : null
  const topProduct = (products[0] && products[0].topProduct) ? 'yes' : null
  const assignee = (result.status !== RENEWAL) ? (statuses && statuses[result.status]?.assignee?.name || 'N/A')
    : (statuses && statuses[APPROVED]?.assignee?.name || 'N/A')
  const createdAt = result.createdAt ? getUTC(result.createdAt, timeFormat) : null
  const owner = result.owner ? result.owner.name : null
  const lastComment = result.comments?.[0]?.message
  const typeSubmissions = getSubmission(result.typeSubmission, result.typeSubmissions)
  let productFamilyName = '', typeSubmission = null

  // Convert productFamily object to string
  if (productFamily) {
    productFamilyName = productFamily.name || ''
  }

  // Convert typeSubmission object to string
  if (typeof typeSubmissions === 'object' && !Array.isArray(typeSubmissions)) {
    typeSubmission = typeSubmissions.name
  } else if (Array.isArray(typeSubmissions)) {
    typeSubmission = typeSubmissions.map(item => item.name).join(', ')
  }

  return {
    id: result.id,
    name: result.name || result.id,
    countryId: result.countryId,
    licenseNumber,
    licenseType,
    approvalDate,
    dateExpiry,
    expectedApprovalDate,
    rejectedDate,
    governmentDate,
    preparedDate,
    requestedDate,
    expectedSubmissionDate,
    actualSubmissionDate,
    registerNumber,
    classification,
    owner,
    status: result.status,
    actualApprovalDate,
    productFamily: productFamilyName,
    typeSubmission,
    checklistAssociated: result.checklistAssociated?.name || result.checklistAssociated?.id || null,
    createdAt,
    comment: lastComment,
    topCountries,
    topProduct,
    assignee
  }
}

const processDataForGrafana = (cachedData, state, type) => {
  const outputResults = []
  const resultArray = processForGrafana[type].process(cachedData, state)

  for (const result of resultArray) {
    const dataResult = { ...result }

    outputResults.push(dataResult)
  }

  return JSON.stringify(outputResults)
}

const processDataForGrafanaBackup = (cachedData, state, type) => {
  const { skuId, products: stateProducts } = state
  const outputResults = []
  
  // Performance tracking
  const startTime = Date.now()
  let processedItems = 0

  console.info('cachedData', cachedData)

  for (const result of cachedData) {
    const { statuses, productFamily, products = [], productFamilyProducts, sku, countryId } = result
    const logs = simplifyLogs(result.logs, result.status)
    const currentLog = getLogByStatus(logs, result.status)
    const requestedLog = getLogByStatus(logs, REQUESTED)
    const approveLog = getLogByStatus(logs, APPROVED)
    const rejectedLog = getLogByStatus(logs, REJECTED)
    const preparedLog = getLogByStatus(logs, PREPARING)
    const governmentLog = getLogByStatus(logs, GOVERNMENT)
    const licenseNumber = currentLog ? currentLog.license : null
    const licenseType = currentLog ? (currentLog.certificate && currentLog.certificate.name) : null
    const approvalDate = getFormattedDate(approveLog, 'dateDiscontinue')
    const dateExpiry = getFormattedDate(approveLog, 'dateExpiry')
    const rejectedDate = getFormattedDate(rejectedLog, 'dateDiscontinue')
    const governmentDate = getFirstAvailableUTC(governmentLog, ['dateStart', 'timestamps'])
    const preparedDate = getFirstAvailableUTC(preparedLog, ['dateStart', 'timestamps'])
    const requestedDate = getFirstAvailableUTC(requestedLog, ['dateSubmitted', 'timestamps'])
    const preparedExpectedSubmissionDate = getStatusDate(statuses, PREPARING, 'expectedSubmissionDate')
    const internalExpectedSubmissionDate = getStatusDate(statuses, INTERNAL, 'expectedSubmissionDate')
    const submittedExpectedSubmissionDate = getStatusDate(statuses, SUBMITTED, 'expectedSubmissionDate')
    const governmentExpectedSubmissionDate = getStatusDate(statuses, GOVERNMENT, 'expectedSubmissionDate')
    const expectedSubmissionDate = getLatestDate(preparedExpectedSubmissionDate, internalExpectedSubmissionDate, submittedExpectedSubmissionDate)
    const governmentActualSubmissionDate = statuses && statuses[GOVERNMENT] && statuses[GOVERNMENT].actualSubmissionDate ? getUTC(statuses[GOVERNMENT].actualSubmissionDate, timeFormat) : ''
    const informationActualSubmissionDate = statuses && statuses[INFORMATION] && statuses[INFORMATION].actualSubmissionDate ? getUTC(statuses[INFORMATION].actualSubmissionDate, timeFormat) : ''
    const actualSubmissionDate = (governmentActualSubmissionDate && informationActualSubmissionDate)
      ? `${governmentActualSubmissionDate}, ${informationActualSubmissionDate}`
      : (governmentActualSubmissionDate || informationActualSubmissionDate)
    const actualApprovalDate = getStatusDate(statuses, APPROVED, 'dateDiscontinue')
    const preparedExpectedApprovalDate = getStatusDate(statuses, PREPARING, 'expectedApprovalDate')
    const internalExpectedApprovalDate = getStatusDate(statuses, INTERNAL, 'expectedApprovalDate')
    const submittedExpectedApprovalDate = getStatusDate(statuses, SUBMITTED, 'expectedApprovalDate')
    const governmentExpectedApprovalDate = getStatusDate(statuses, GOVERNMENT, 'expectedApprovalDate')
    const expectedApprovalDate = getLatestDate(preparedExpectedApprovalDate, internalExpectedApprovalDate, submittedExpectedApprovalDate, governmentExpectedApprovalDate)
    const registerNumber = preparedLog ? preparedLog.registerNumber : null
    const classification = result.classification?.length > 0 ? result.classification.find(c => c.id === result.country)?.name : null
    const topCountries = (type === '15' && products[0] && products[0].topCountries) ? products[0].topCountries.join(', ') : null
    const topProduct = (products[0] && products[0].topProduct) ? 'yes' : null
    const assignee = (result.status !== RENEWAL) ? (statuses && statuses[result.status]?.assignee?.name || 'N/A')
      : (statuses && statuses[APPROVED]?.assignee?.name || 'N/A')
    let productFamilyName = '', includedSku = [], typeSubmission = null
    const skuNames = {}, partNumbers = {}
    const createdAt = result.createdAt ? getUTC(result.createdAt, timeFormat) : null
    const owner = result.owner ? result.owner.name : null
    const lastComment = result.comments?.[0]?.message
    const typeSubmissions = getSubmission(result.typeSubmission, result.typeSubmissions)

    if (!type.includes('16')) {
      if (Array.isArray(products) && products.length && ['4', '3', '6', '8', '9', '10', '11', '12', '13', '14'].some(t => type.includes(t))) {
        const product = products[0]
        const productSku = sku.filter(item => item.productId && item.productId.toString() === product._id.toString())    

        if (!productSku.length) continue
      }

      if (productFamily) {
        productFamilyName = productFamily.name || ''

        if (skuId) {
          productFamily.products = [ filterFamilyProducts(productFamilyProducts, skuId) ]
        }

        if (stateProducts && stateProducts.length && productFamilyProducts) {
          productFamily.products = productFamilyProducts.filter(product => stateProducts.includes(product._id.toString()))
        }

        if (productFamily.products && productFamilyProducts && productFamilyProducts.length) {
          products.push(...productFamily.products)
        }
      }
    }

    if (skuId && products.length) {
      products.forEach(product => {
        if (product.sku && product.sku.length) {
          product.sku = product.sku.filter(skuItem => skuItem._id.toString() === skuId.toString())
        }
      })
    }

    includedSku = accumulateSku(result, countryId, sku, {}, stateProducts?.length)

    for (const p of products) {
      if (!p._id) continue
      
      for (const skuItem of sku) {
        const key = `${p._id.toString()}_${skuItem._id.toString()}`

        skuNames[key] = []

        partNumbers[key] = (skuItem.partNumbers && skuItem.partNumbers.length > 0)
          ? skuItem.partNumbers
          : getPartNumbers(skuItem.countriesInfo, countryId)

        if (includedSku?.map(item => item._id.toString()).includes(skuItem._id.toString())) {
          skuNames[key].push(skuItem)
        }
      }
    }

    if (typeof typeSubmissions === 'object' && !Array.isArray(typeSubmissions)) {
      typeSubmission = typeSubmissions.name
    } else if (Array.isArray(typeSubmissions)) {
      typeSubmission = typeSubmissions.map(item => item.name).join(', ')
    }

    const createDataResult = (data = {}) => ({
      id: result.id,
      name: result.name || result.id,
      countryId: result.countryId,
      licenseNumber,
      licenseType,
      approvalDate,
      dateExpiry,
      expectedApprovalDate,
      rejectedDate,
      governmentDate,
      preparedDate,
      requestedDate,
      expectedSubmissionDate,
      actualSubmissionDate,
      registerNumber,
      classification,
      owner,
      status: result.status,
      actualApprovalDate,
      productFamily: productFamilyName,
      typeSubmission,
      checklistAssociated: result.checklistAssociated?.name || result.checklistAssociated?.id || null,
      createdAt,
      ...data
    })

    const processOutputResults = (productData = {}, additionalData = {}) => {
      const dataResult = createDataResult({
        product: productData.name || '',
        sku: productData.sku || '',
        skuDescription: productData.skuDescription || '',
        partNumber: productData.partNumber || '',
        comment: (type === '16') ? lastComment : undefined,
        ...additionalData
      })

      outputResults.push(dataResult)
    }

    if (type === '18') {
      const dateStartReasonList = getDiscontinueWithDateFormat(result.logs)
      const productNames = (productFamilyProducts?.length > 0 ? productFamilyProducts : products).map(({ name }) => name).join(', ')

      dateStartReasonList.forEach(({ startDate, reason }) => {
        processOutputResults({}, { partNumber: '', product: productNames, startDate, reason })
      })
    } else if (!products.length) {
      const additionalData = { comment: lastComment }

      if (type === '1') {
        additionalData.min = result.min
        additionalData.max = result.max
        additionalData.avg = result.avg
      }
      processOutputResults({}, additionalData)
    } else {
      for (const product of products) {
        const pid = product._id?.toString() || product

        for (const skuItem of includedSku) {
          const key = `${pid}_${skuItem._id.toString()}`
          const partNumbersList = partNumbers[key]
          const partNumber = (partNumbersList && partNumbersList.length > 0)
            ? partNumbersList.map(item => item.name).join(', ')
            : null
          const skuNamesList = skuNames[key]

          if (skuNamesList?.length) {
            for (const skuObj of skuNamesList) {
              const additionalData = { partNumber, sku: skuObj.name, skuDescription: skuObj.description, product: product.name, topCountries, topProduct }

              if (type === '19') { 
                Object.assign(additionalData, {
                  assignee,
                  preparedExpectedSubmissionDate,
                  governmentExpectedSubmissionDate,
                  governmentActualSubmissionDate,
                  preparedExpectedApprovalDate,
                  checklistAssociated: result.checklistAssociated?.name || result.checklistAssociated?.id || null,
                })
              }

              processOutputResults({}, additionalData)
            }
          } else {
            const additionalData = { sku: '', skuDescription: '', product: product.name, topCountries, topProduct }

            if (type === '19') { 
              Object.assign(additionalData, {
                assignee,
                preparedExpectedSubmissionDate,
                governmentExpectedSubmissionDate,
                governmentActualSubmissionDate,
                preparedExpectedApprovalDate,
              })
            }

            processOutputResults({}, additionalData)
          }
        }
      }
    }
  }

  // Log performance stats
  const endTime = Date.now()
  const elapsedMs = endTime - startTime
  const itemsPerSecond = processedItems > 0 ? (processedItems * 1000 / elapsedMs) : 0

  console.log(`processDataForGrafana: Processed ${processedItems} items in ${elapsedMs}ms (${itemsPerSecond.toFixed(2)} items/s)`)
  
  return JSON.stringify(outputResults)
}

module.exports = { getGrafanaReports, processDataForGrafana, processDataForGrafanaBackup }
