const fs = require('fs')
const { randomUUID } = require('crypto')
const tmp = require('tmp')
const JSZip = require('jszip')
const moment = require('moment')
const db = require('../../../db')
const { sendProgress, catchError } = require('../../Export/utils')
const S3 = require('../../../util/S3')
const { grafanaDashboardEmbed, grafanaCache } = require('../../Grafana/shared')
const { processDataForGrafana } = require('./Grafana')
const ReactDOMServer = require('react-dom/server')

require('@babel/register')({ presets: ['@babel/preset-react'] })

const { showReportTitle, filterBySelectedState } = require('./shared')
const { OPTIMAL_ROWS_COUNT, REPORTS_EXCEL_OPTIONS } = require('../../../util/excel/constants')
const { workerGenerateExcel } = require('../../../util/worker-pool')
const { getFileRange } = require('../../Checklist/Reports/shared')
const { getFilters } = require('./Trackings')
const { splitIntoChunks } = require('../../../util/Functions')

const reports = {
  '1': {
    isCustomWorkflow: true,
    handler: require('./TimeForPreparation'),
  },
  '2': {
    getName: (state, t) => `Countries in which ${showReportTitle({ ...state, t })} have been approved`,
    getData: require('./CountriesWithMarketApproval/getData'),
    getColumns: require('./CountriesWithMarketApproval/component').getColumns,
    processData: require('./CountriesWithMarketApproval/component').processData,
  },
  '3': {
    getName: (state, t) => `Status of License Type Report by ${showReportTitle({ ...state, t, one: true })}`,
    getData: require('./StatusOfLicenseType/getData'),
    getColumns: require('./StatusOfLicenseType/component').getColumns,
    processData: require('./StatusOfLicenseType/component').processData,
  },
  '4': {
    getName: () => 'Time to Prepare Application in a Country',
    getData: require('./TimeToPrepareApplication/getData'),
    getColumns: require('./TimeToPrepareApplication/component').getColumns,
    processData: require('./TimeToPrepareApplication/component').processData,
  },
  '5': {
    isCustomWorkflow: true,
    handler: require('./TotalGovernmentReviewTime'),
  },
  '6': {
    getName: () => 'Status of a submission type globally',
    getData: require('./StatusOfSubmissionType/getData'),
    getColumns: require('./StatusOfSubmissionType/component').getColumns,
    processData: require('./StatusOfSubmissionType/component').processData,
  },
  '7': {
    getName: (state, t) => `Status of ${showReportTitle({ ...state, t })} in a Country`,
    getData: require('./StatusOfProduct/getData'),
    getColumns: require('./StatusOfProduct/component').getColumns,
    processData: require('./StatusOfProduct/component').processData,
  },
  '8': {
    getName: (state, t) => `${t('Glossary.Product_other')} ready for Sale`,
    getData: require('./ReadyForSaleProducts/getData'),
    getColumns: require('./ReadyForSaleProducts/component').getColumns,
    processData: require('./ReadyForSaleProducts/component').processData,
  },
  '9': {
    getName: (state, t) => `Countries where ${showReportTitle({ ...state, t })} can be sold`,
    getData: require('./CountriesWhereProductIsAllowed/getData'),
    getColumns: require('./CountriesWhereProductIsAllowed/component').getColumns,
    processData: require('./CountriesWhereProductIsAllowed/component').processData,
  },
  '10': {
    getName: (state, t) => `Status of ${showReportTitle({ ...state, t })} in MDSAP Countries`,
    getData: require('./StatusOfProductInMDSAP/getData'),
    getColumns: require('./StatusOfProductInMDSAP/component').getColumns,
    processData: require('./StatusOfProductInMDSAP/component').processData,
  },
  '11': {
    getName: () => 'Registration Status',
    getData: require('./RegistrationStatus/getData'),
    getColumns: require('./RegistrationStatus/component').getColumns,
    processData: require('./RegistrationStatus/component').processData,
  },
  '12': {
    getName: () => 'Registration Status for Change Assessment',
    getData: require('./RegistrationStatusForChangeAssessment/getData'),
    getColumns: require('./RegistrationStatusForChangeAssessment/component').getColumns,
    processData: require('./RegistrationStatusForChangeAssessment/component').processData,
  },
  '13': {
    getName: () => 'Total Approval time in [Country]',
    getData: require('./TotalApprovalTime/getData'),
    getColumns: require('./TotalApprovalTime/component').getColumns,
    processData: require('./TotalApprovalTime/component').processData,
  },
  '14': {
    getName: (state) => state.timeRangeType
      ? `License expiration by a time period
        from ${moment(state.timeRangeDate[0]).getUTC()}
        to ${moment(state.timeRangeDate[1]).getUTC()}`
      : 'License expiration by a time period',
    getData: require('./LicenseExpirationByTime/getData'),
    getColumns: require('./LicenseExpirationByTime/component').getColumns,
    processData: require('./LicenseExpirationByTime/component').processData,
  },
  '15': {
    isCustomWorkflow: true,
    handler: require('./KPIsOfStatusProgress'),
  },
  '16': {
    getName: (state) => `Regulatory Status of ${state.productName || state.productFamily && state.productFamily.name}`,
    getData: require('./RegulatoryStatusOfProduct/getData'),
    getColumns: require('./RegulatoryStatusOfProduct/component').getColumns,
    processData: require('./RegulatoryStatusOfProduct/component').processData,
  },
  '17': {
    getName: () => 'Registrations in Process',
    getData: require('./RegistrationsInProcess/getData'),
    getColumns: require('./RegistrationsInProcess/component').getColumns,
    processData: require('./RegistrationsInProcess/component').processData,
  },
  '18': {
    isCustomWorkflow: true,
    handler: require('./NumberOfAdditionalInformationRequests'),
  },
  '19': {
    isCustomWorkflow: true,
    handler: require('./MonthlyOnTimeDelivery'),
  },
}

const VALID_REPORT_TYPES = Object.keys(reports)

module.exports = async(req, res) => {

  const { state } = req.body
  const { type } = req.params
  const { t } = req
  const creator = req.sub_user ? req.sub_user._id : req.user._id

  if (!VALID_REPORT_TYPES.includes(type)) return res.sendError('002', 'type')

  if (type === '17' && ![1, 2].includes(state.outputFormat)) {
    return res.sendError('002', 'outputFormat')
  }

  if (reports[type].isCustomWorkflow) return reports[type].handler(req, res)

  let docZip, docPdf
  const tmpZip = tmp.fileSync({ postfix: '.zip' })

  try {
    const docs = []
    const { skuIds = [], skus = [], outputFormat } = state

    if (skus?.length || skuIds?.length) {
      state.skuNames = await db.sku.distinct('name', { _id: { $in: skus.length ? skus : skuIds } })
    }

    let tag = await db.tag.findOne({
      name: 'Report Tracking',
      owner: req.user._id
    })

    if (!tag) {
      tag = await db.tag.create({
        name: 'Report Tracking',
        owner: req.user._id,
        extra: { req, action: 'UploadDoc' }
      })
    }

    const name = reports[type].getName(state, t)

    docZip = await db.doc.create({
      owner: req.user._id,
      name: `${name}.zip`,
      progress: 0,
      creator,
      tags: [tag._id],
      extra: { req, action: 'UploadDoc' }
    })

    docPdf = await db.doc.create({
      owner: req.user._id,
      name: `${name}.pdf`,
      progress: 0,
      creator,
      tags: [tag._id],
    })

    docs.push(docZip)
    docs.push(docPdf)

    res.json({ docs })

    await sendProgress(0, docZip, creator)

    const filters = await getFilters(req, type, state)
    const { getData, getIds } = reports[type].getData
    const { processData, getColumns } = reports[type]

    await sendProgress(20, docZip, creator)

    const OPTIMAL_QUERY_COUNT = 200
    let fileNameCount = 0
    const excelData = []
    const zip = new JSZip()
    const columns = getColumns(t, state)

    await sendProgress(30, docZip, creator)

    let uniqueReportId = Date.now()
    const formattedType = type === '17' ? `17${outputFormat === 1 ? 'A' : 'B'}` : type

    const ids = await getIds(filters)
    const chunks = splitIntoChunks(ids, OPTIMAL_QUERY_COUNT)
    const previousState = {} // use this if you need to save some info between chunks of processing

    for (const [index, trackingIds] of chunks.entries()) {
      const trackings = await getData({ owner: req.user._id, ids: trackingIds })

      uniqueReportId = await grafanaCache({ uniqueReportId, owner: req.user._id, results: trackings, type: formattedType, filters, skip: index, limit: OPTIMAL_QUERY_COUNT, state, module: 'tracking', processDataForGrafana })

      filterBySelectedState(trackings, state)

      const result = processData({ trackings, columns, state, previousState })

      result.forEach(i => excelData.push(i))

      const dataLength = excelData.length

      for (let i = 0; i < dataLength; i += OPTIMAL_ROWS_COUNT) {
        if (excelData.length >= OPTIMAL_ROWS_COUNT) {
          const data = excelData.splice(0, OPTIMAL_ROWS_COUNT)
          const excel = await workerGenerateExcel([{ data, columns, options: REPORTS_EXCEL_OPTIONS }])
          const fileName = `files/${name}_report__${getFileRange(fileNameCount, OPTIMAL_ROWS_COUNT)}.xlsx`

          zip.file(fileName, excel, { binary: true })

          fileNameCount++
        }
      }

      const percentage = Math.round(30 + (index / chunks.length) * 30) // from 30 to 60

      await sendProgress(percentage, docZip, creator)
    }

    const contentStr = await grafanaDashboardEmbed({ module: 'tracking', type: formattedType, grafanaReport: uniqueReportId, pdfId: docPdf._id })
    const content = ReactDOMServer.renderToString(contentStr)

    await sendProgress(0, docPdf, creator, content)

    if (excelData.length > 0) {
      const fileName = fileNameCount > 0
        ? `files/${name}_report__${getFileRange(fileNameCount, OPTIMAL_ROWS_COUNT)}.xlsx`
        : `${name}.xlsx`
      const excel = await workerGenerateExcel([{ data: excelData, columns, options: REPORTS_EXCEL_OPTIONS }])

      zip.file(fileName, excel, { binary: true })
    }

    const key = `/tmp/reports/${req.user._id}/${randomUUID()}`

    zip
      .generateNodeStream({ type: 'nodebuffer', streamFiles: true })
      .pipe(fs.createWriteStream(tmpZip.name))
      .on('finish', async() => {
        await sendProgress(65, docZip, creator)

        const body = fs.readFileSync(tmpZip.name)

        await sendProgress(70, docZip, creator)
        await S3.upload({ key, body })
        await sendProgress(90, docZip, creator)

        const stats = fs.statSync(tmpZip.name)
        const fileSizeInBytes = stats.size

        docZip.mimetype = '7bit'
        docZip.size = fileSizeInBytes
        docZip.progress = 100
        docZip.path = key

        await docZip.save()
        await sendProgress(100, docZip, creator)

        tmpZip.removeCallback()
      })

  } catch (error) {
    catchError(docZip, creator)
    res.logError(error)
    tmpZip.removeCallback()
  }
}
